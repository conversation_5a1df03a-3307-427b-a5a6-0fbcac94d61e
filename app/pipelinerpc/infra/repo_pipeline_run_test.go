package infra

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/testfactory"
	"code.byted.org/devinfra/hagrid/libs/connections/sqlite"
	. "github.com/bytedance/mockey"
	json "github.com/bytedance/sonic"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetPipelineRun(t *testing.T) {
	t.Skip()
	testfactory.InitBoeMysql()
	ctx := context.Background()
	pipelineRun, err := PipelineRunRepo().GetPipelineRun(ctx, 1435317760, false)
	assert.Nil(t, err)
	var assignmentIDs []uint64
	if pipelineRun.VarAssignmentIds != nil {
		err = json.Unmarshal(pipelineRun.VarAssignmentIds, &assignmentIDs)
		// if var assignment ids is nil, unmarshal will failed
		assert.Nil(t, err)
	}
}

func TestFindLastByPipelineIdIn(t *testing.T) {
	ctx := context.Background()
	db := sqlite.MustInitialize()
	_ = db.AutoMigrate(new(entity.SimplePipelineRun))
	repo := newPipelineRunRepo(db)

	value1 := &entity.SimplePipelineRun{RunId: 1, PipelineId: 1, Status: entity.PipelineRunStatusWaiting}
	id1, _ := repo.Create(ctx, value1)
	defer func() { _ = repo.DeleteById(ctx, id1) }()

	value2 := &entity.SimplePipelineRun{RunId: 2, PipelineId: 1, Status: entity.PipelineRunStatusWaiting}
	id2, _ := repo.Create(ctx, value2)
	defer func() { _ = repo.DeleteById(ctx, id2) }()

	value3 := &entity.SimplePipelineRun{RunId: 1, PipelineId: 2, Status: entity.PipelineRunStatusWaiting}
	id3, _ := repo.Create(ctx, value3)
	defer func() { _ = repo.DeleteById(ctx, id3) }()

	actual, _ := repo.FindLastByPipelineIdIn(ctx, []uint64{1, 2})
	assert.Equal(t, 2, len(actual))
	assert.Equal(t, uint64(1), actual[0].PipelineId)
	assert.Equal(t, uint64(2), actual[0].RunId)
	assert.Equal(t, uint64(2), actual[1].PipelineId)
	assert.Equal(t, uint64(1), actual[1].RunId)
}

func Test_visiblePipelineRun(t *testing.T) {
	type args struct {
		db *gorm.DB
	}
	type testConfig struct {
		args args
		want IPipelineRunRepo
	}
	PatchConvey("test", t, func() {
		testfactory.InitMemMysql()
		mysql.DB.Create(&entity.PipelineRun{RunId: 1, RunSeq: 1, PipelineId: 10, IsInvisible: true})
		mysql.DB.Create(&entity.PipelineRun{RunId: 2, RunSeq: 2, PipelineId: 10, IsInvisible: false})

		PatchConvey("By default", func() {
			tt := testConfig{
				args: args{db: mysql.DB.Scopes(visiblePipelineRun)},
			}
			var runs []*entity.PipelineRun
			err := tt.args.db.Where("pipeline_id =?", 10).Find(&runs).Error
			So(err, ShouldBeNil)
			So(len(runs), ShouldEqual, 1)
			So(runs[0].RunId, ShouldEqual, 2)
		})
		PatchConvey("Unscoped", func() {
			tt := testConfig{
				args: args{db: mysql.DB},
			}
			var runs []*entity.PipelineRun
			err := tt.args.db.Where("pipeline_id =?", 10).Find(&runs).Error
			So(err, ShouldBeNil)
			So(len(runs), ShouldEqual, 2)
		})
	})
}

func Test_pipelineRunRepo_UpdatePipelineRunNote(t *testing.T) {
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx   context.Context
		runID uint64
		note  string
	}
	type testConfig struct {
		fields  fields
		args    args
		want    *entity.PipelineRun
		wantErr bool
	}
	PatchConvey("Update", t, func() {
		testfactory.InitMemMysql()

		// 插入两条数据
		mysql.DB.Create(&entity.PipelineRun{
			RunId: 1,
		})
		mysql.DB.Create(&entity.PipelineRun{
			RunId: 2,
		})
		PatchConvey("normal", func() {
			tt := testConfig{
				args: args{
					ctx:   context.TODO(),
					runID: 1,
					note:  "test",
				},
				wantErr: false,
				fields: fields{
					db: mysql.DB,
				},
			}
			p := &pipelineRunRepo{
				db: tt.fields.db,
			}
			err := p.UpdatePipelineRunNote(tt.args.ctx, tt.args.runID, tt.args.note)
			So(err != nil, ShouldEqual, tt.wantErr)

			r := entity.PipelineRun{}
			mysql.DB.Where("run_id =?", tt.args.runID).First(&r)
			So(r.RunId, ShouldEqual, tt.args.runID)
			So(r.Note, ShouldEqual, "test")
		})
		PatchConvey("record not found", func() {
			tt := testConfig{
				args: args{
					ctx:   context.TODO(),
					runID: 3,
					note:  "test",
				},
				wantErr: true,
				fields: fields{
					db: mysql.DB,
				},
			}
			p := &pipelineRunRepo{
				db: tt.fields.db,
			}
			err := p.UpdatePipelineRunNote(tt.args.ctx, tt.args.runID, tt.args.note)

			So(err != nil, ShouldEqual, tt.wantErr)
		})
	})
}

func TestPipelineRepo_GetLatestPipelineRunsByYamlNames(t *testing.T) {
	t.Skip()
	testfactory.InitBoeMysql()
	ctx := context.Background()
	type Case struct {
		repo   string
		branch string
		tag    string
		files  []string
	}
	cases := []Case{
		{
			repo:   "wangjinbao/wjbtest1",
			branch: "testci",
			tag:    "some_tag",
			files:  []string{"blan.yml", "blank20.yml"},
		},
		{
			repo:   "wangjinbao/wjbtest1",
			branch: "testci",
			tag:    "some_tag",
			files:  []string{"blan1.yml", "blank20.yml"},
		},
		{
			repo:   "wangjinbao/wjbtest1",
			branch: "ci_demo",
			tag:    "some_tag",
			files:  []string{"blan1.yml", "blank.yml"},
		},
		{
			repo:   "devops/liuyang_atomservice",
			branch: "master",
			tag:    "some_tag",
			files:  []string{"cjq.yml", "blank.yml"},
		},
		{
			repo:   "tce/zzmtest1",
			branch: "feat/new_zxm3",
			tag:    "some_tag",
			files:  []string{"test.yaml", "test_ci.yaml"},
		},
	}
	for _, c := range cases {
		runs, err := PipelineRunRepo().GetLatestPipelineRunsByYamlNames(ctx, c.repo, c.branch, c.tag, c.files)
		assert.Nil(t, err)
		newRuns, err := GetLatestPipelineRunsByYamlFile(ctx, c.repo, c.branch, c.files)
		assert.Nil(t, err)
		assert.True(t, reflect.DeepEqual(runs, newRuns))
	}
}

func TestGetMaxPipelineRunSeq(t *testing.T) {
	//t.Skip()
	testfactory.InitMemMysql()
	ctx := context.Background()
	max, err := PipelineRunRepo().GetMaxPipelineRunSeq(ctx, 115489536000)
	assert.Nil(t, err)
	fmt.Println(max)
}
func TestGetInvisibleMaxPipelineRunSeq(t *testing.T) {
	t.Skip()
	testfactory.InitBoeMysql()
	ctx := context.Background()
	max, err := PipelineRunRepo().GetInvisibleMaxPipelineRunSeq(ctx, 531479420)
	assert.Nil(t, err)
	fmt.Println(max)
}

func TestGetLatestPipelineAndRunIDsByTriggerUser(t *testing.T) {
	t.Skip()
	testfactory.InitBoeMysql()
	ctx := context.Background()
	_, pipelineRunPair, err := PipelineRunRepo().GetLatestPipelineAndRunIDsByTriggerUser(ctx, "liuyang.leon", 10, 0)
	assert.Nil(t, err)
	fmt.Println(pipelineRunPair)
}

func Test_pipelineRunRepo_GetPipelineRunsByYamlFile(t *testing.T) {
	t.Skip()
	testfactory.InitBoeMysql()
	ctx := context.Background()

	cases := []*SearchGitPipelineRunOpts{
		{
			GitRepo:   "devops/liuyang_atomservice",
			CommitSha: "d0847cadc5b17c062977b2024cba3c9cdd198067",
		},
	}
	for _, c := range cases {
		runs, _, err := PipelineRunRepo().GetPipelineRunsByYamlFile(ctx, c)
		assert.Nil(t, err)
		println(len(runs))
	}
}

func Test_UpdatePipelineRunNoteByEngineRunId(t *testing.T) {
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx         context.Context
		engineRunID uint64
		note        string
	}
	type testConfig struct {
		fields  fields
		args    args
		want    *entity.PipelineRun
		wantErr bool
	}
	PatchConvey("Update", t, func() {
		testfactory.InitMemMysql()

		// 插入两条数据
		mysql.DB.Create(&entity.PipelineRun{
			EngineRunId: 10,
		})
		mysql.DB.Create(&entity.PipelineRun{
			EngineRunId: 11,
		})
		PatchConvey("normal", func() {
			tt := testConfig{
				args: args{
					ctx:         context.TODO(),
					engineRunID: 10,
					note:        "System cancelled：MR opened",
				},
				wantErr: false,
				fields: fields{
					db: mysql.DB,
				},
			}
			p := &pipelineRunRepo{
				db: tt.fields.db,
			}
			err := p.UpdatePipelineRunNoteByEngineRunId(tt.args.ctx, tt.args.engineRunID, tt.args.note)
			So(err != nil, ShouldEqual, tt.wantErr)

			r := entity.PipelineRun{}
			mysql.DB.Where("engine_run_id = ?", tt.args.engineRunID).First(&r)
			So(r.EngineRunId, ShouldEqual, tt.args.engineRunID)
			So(r.Note, ShouldEqual, "System cancelled：MR opened")
		})
		PatchConvey("record not found", func() {
			tt := testConfig{
				args: args{
					ctx:         context.TODO(),
					engineRunID: 12,
					note:        "System cancelled：MR opened",
				},
				wantErr: true,
				fields: fields{
					db: mysql.DB,
				},
			}
			p := &pipelineRunRepo{
				db: tt.fields.db,
			}
			err := p.UpdatePipelineRunNote(tt.args.ctx, tt.args.engineRunID, tt.args.note)

			So(err != nil, ShouldEqual, tt.wantErr)
		})
	})
}

func Test_UpdatePipelineRun(t *testing.T) {
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx   context.Context
		runId uint64
		run   *entity.PipelineRun
	}
	type testConfig struct {
		fields  fields
		args    args
		want    *entity.PipelineRun
		wantErr bool
	}
	PatchConvey("Update", t, func() {
		testfactory.InitMemMysql()

		// 插入两条数据
		mysql.DB.Create(&entity.PipelineRun{
			RunId: 10,
		})
		mysql.DB.Create(&entity.PipelineRun{
			RunId: 11,
		})
		PatchConvey("normal", func() {
			tt := testConfig{
				args: args{
					ctx:   context.TODO(),
					runId: 10,
					run: &entity.PipelineRun{
						RunId:            10,
						RunParams:        nil,
						VarAssignmentIds: nil,
					},
				},
				wantErr: false,
				fields: fields{
					db: mysql.DB,
				},
			}
			p := &pipelineRunRepo{
				db: tt.fields.db,
			}
			err := p.UpdatePipelineRun(tt.args.ctx, tt.args.runId, tt.args.run)
			So(err != nil, ShouldEqual, tt.wantErr)

			r := entity.PipelineRun{}
			mysql.DB.Where("run_id = ?", tt.args.runId).First(&r)
			So(r.RunId, ShouldEqual, tt.args.runId)
			So(r.Note, ShouldNotBeNil)
		})
		PatchConvey("empty", func() {
			tt := testConfig{
				args: args{
					ctx:   context.TODO(),
					runId: 0,
					run:   &entity.PipelineRun{},
				},
				wantErr: false,
				fields: fields{
					db: mysql.DB,
				},
			}
			p := &pipelineRunRepo{
				db: tt.fields.db,
			}
			err := p.UpdatePipelineRun(tt.args.ctx, tt.args.runId, tt.args.run)

			So(err != nil, ShouldEqual, tt.wantErr)
		})
	})
}

func TestGetFirstPipelineRunByPipelineIDAndStatus(t *testing.T) {
	PatchConvey("GetFirstPipelineRunByPipelineIDAndStatus", t, func() {
		testfactory.InitMemMysql()
		ctx := context.Background()

		// 准备测试数据
		pipelineID := uint64(1001)

		// 创建多个不同状态的 pipeline run，按 id 顺序插入
		runs := []*entity.PipelineRun{
			{RunId: 1, PipelineId: pipelineID, Status: entity.PipelineRunStatusBlocking, RunSeq: 1},
			{RunId: 2, PipelineId: pipelineID, Status: entity.PipelineRunStatusRunning, RunSeq: 2},
			{RunId: 3, PipelineId: pipelineID, Status: entity.PipelineRunStatusBlocking, RunSeq: 3},
			{RunId: 4, PipelineId: pipelineID, Status: entity.PipelineRunStatusSucceeded, RunSeq: 4},
			{RunId: 5, PipelineId: 2002, Status: entity.PipelineRunStatusBlocking, RunSeq: 1}, // 不同的 pipeline
		}

		for _, run := range runs {
			mysql.DB.Create(run)
		}

		repo := &pipelineRunRepo{
			db: mysql.DB.Scopes(visiblePipelineRun),
		}

		PatchConvey("单个状态查询 - 找到第一个匹配记录", func() {
			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID, entity.PipelineRunStatusBlocking)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(result.RunId, ShouldEqual, 1) // 应该返回第一个 blocking 状态的记录
			So(result.PipelineId, ShouldEqual, pipelineID)
			So(result.Status, ShouldEqual, entity.PipelineRunStatusBlocking)
		})

		PatchConvey("单个状态查询 - 未找到匹配记录", func() {
			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID, entity.PipelineRunStatusFailed)

			So(err, ShouldBeNil)
			So(result, ShouldBeNil) // 没有找到记录应该返回 nil
		})

		PatchConvey("多个状态查询 - 找到第一个匹配记录", func() {
			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID,
				entity.PipelineRunStatusRunning, entity.PipelineRunStatusSucceeded)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(result.RunId, ShouldEqual, 2) // 应该返回第一个 running 状态的记录
			So(result.PipelineId, ShouldEqual, pipelineID)
			So(result.Status, ShouldEqual, entity.PipelineRunStatusRunning)
		})

		PatchConvey("多个状态查询 - 未找到匹配记录", func() {
			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID,
				entity.PipelineRunStatusFailed, entity.PipelineRunStatusCancelled)

			So(err, ShouldBeNil)
			So(result, ShouldBeNil) // 没有找到记录应该返回 nil
		})

		PatchConvey("查询不存在的 pipeline ID", func() {
			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, uint64(9999), entity.PipelineRunStatusBlocking)

			So(err, ShouldBeNil)
			So(result, ShouldBeNil) // 没有找到记录应该返回 nil
		})

		PatchConvey("空状态列表查询", func() {
			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID)

			So(err, ShouldBeNil)
			So(result, ShouldBeNil) // 空状态列表应该返回 nil
		})

		PatchConvey("测试 invisible 记录过滤", func() {
			// 创建一个 invisible 的记录
			invisibleRun := &entity.PipelineRun{
				RunId: 100,
				PipelineId: pipelineID,
				Status: entity.PipelineRunStatusWaiting,
				RunSeq: 100,
				IsInvisible: true,
			}
			mysql.DB.Create(invisibleRun)

			// 使用带 visiblePipelineRun scope 的 repo 查询
			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID, entity.PipelineRunStatusWaiting)

			So(err, ShouldBeNil)
			So(result, ShouldBeNil) // invisible 记录应该被过滤掉

			// 使用不带 scope 的 repo 查询
			unscopedRepo := &pipelineRunRepo{db: mysql.DB}
			result2, err2 := unscopedRepo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID, entity.PipelineRunStatusWaiting)

			So(err2, ShouldBeNil)
			So(result2, ShouldNotBeNil)
			So(result2.RunId, ShouldEqual, 100) // 不带 scope 应该能查到 invisible 记录
		})

		PatchConvey("测试按 ID 排序", func() {
			// 清理之前的数据
			mysql.DB.Where("pipeline_id = ?", pipelineID).Delete(&entity.PipelineRun{})

			// 创建多个相同状态的记录，但插入顺序与 ID 顺序不同
			runs := []*entity.PipelineRun{
				{RunId: 103, PipelineId: pipelineID, Status: entity.PipelineRunStatusWaiting, RunSeq: 3},
				{RunId: 101, PipelineId: pipelineID, Status: entity.PipelineRunStatusWaiting, RunSeq: 1},
				{RunId: 102, PipelineId: pipelineID, Status: entity.PipelineRunStatusWaiting, RunSeq: 2},
			}

			// 按非顺序插入
			for _, run := range runs {
				mysql.DB.Create(run)
			}

			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID, entity.PipelineRunStatusWaiting)

			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(result.RunId, ShouldEqual, 101) // 应该返回 ID 最小的记录
		})
	})
}

func TestGetFirstPipelineRunByPipelineIDAndStatus_ErrorCases(t *testing.T) {
	PatchConvey("GetFirstPipelineRunByPipelineIDAndStatus 错误场景", t, func() {
		testfactory.InitMemMysql()
		ctx := context.Background()

		PatchConvey("数据库连接错误", func() {
			// 创建一个无效的数据库连接
			invalidDB := &gorm.DB{}
			repo := &pipelineRunRepo{db: invalidDB}

			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, uint64(1), entity.PipelineRunStatusBlocking)

			So(err, ShouldNotBeNil)
			So(result, ShouldBeNil)
		})

		PatchConvey("测试 perror.GetBitsError 处理", func() {
			// 这个测试需要模拟数据库错误，但由于 perror.GetBitsError 的复杂性，
			// 我们主要验证错误处理路径的存在
			repo := &pipelineRunRepo{db: mysql.DB.Scopes(visiblePipelineRun)}

			// 使用一个会导致数据库错误的场景（比如表不存在）
			mysql.DB.Exec("DROP TABLE IF EXISTS pipeline_run")

			result, err := repo.GetFirstPipelineRunByPipelineIDAndStatus(ctx, uint64(1), entity.PipelineRunStatusBlocking)

			So(err, ShouldNotBeNil)
			So(result, ShouldBeNil)

			// 重新创建表以免影响其他测试
			mysql.DB.AutoMigrate(&entity.PipelineRun{})
		})
	})
}
